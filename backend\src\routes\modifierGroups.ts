import { FastifyInstance, FastifyReply, FastifyRequest } from "fastify";
import { z } from "zod";
import { prisma } from "../index";

// JWT payload tipi
interface JWTPayload {
  id: string;
  role: string;
  branchId: string | null;
  companyId: string;
}

// Fastify instance'ını auth plugin decorators ile genişlet
declare module "fastify" {
  interface FastifyInstance {
    authenticate: (
      request: FastifyRequest,
      reply: FastifyReply
    ) => Promise<void>;
    authorize: (
      roles: string[]
    ) => (request: FastifyRequest, reply: FastifyReply) => Promise<void>;
  }
}

// Validation şemaları
const createModifierGroupSchema = z
  .object({
    name: z
      .string()
      .min(2, "Modifiye grup adı en az 2 karakter olmalıdır")
      .max(100, "Modifiye grup adı en fazla 100 karakter olabilir"),
    description: z.string().optional(),
    minSelection: z
      .number()
      .int()
      .min(0, "Minimum seçim sayısı negatif olamaz")
      .default(0),
    maxSelection: z
      .number()
      .int()
      .min(1, "Maksimum seçim sayısı en az 1 olmalıdır")
      .default(1),
    required: z.boolean().default(false),
    freeSelection: z
      .number()
      .int()
      .min(0, "Ücretsiz seçim sayısı negatif olamaz")
      .default(0),
    displayOrder: z
      .number()
      .int()
      .min(0, "Görüntüleme sırası negatif olamaz")
      .default(0),
    active: z.boolean().default(true),
  })
  .refine(data => data.minSelection <= data.maxSelection, {
    message: "Minimum seçim sayısı maksimum seçim sayısından büyük olamaz",
    path: ["minSelection"],
  })
  .refine(data => data.freeSelection <= data.maxSelection, {
    message: "Ücretsiz seçim sayısı maksimum seçim sayısından büyük olamaz",
    path: ["freeSelection"],
  });

const updateModifierGroupSchema = createModifierGroupSchema.partial();

const querySchema = z.object({
  page: z
    .string()
    .transform(val => parseInt(val))
    .pipe(z.number().int().min(1))
    .default("1"),
  limit: z
    .string()
    .transform(val => parseInt(val))
    .pipe(z.number().int().min(1).max(100))
    .default("20"),
  search: z.string().optional(),
  active: z
    .string()
    .transform(val => val === "true")
    .pipe(z.boolean())
    .optional(),
  required: z
    .string()
    .transform(val => val === "true")
    .pipe(z.boolean())
    .optional(),
  sortBy: z.enum(["name", "displayOrder", "createdAt"]).default("displayOrder"),
  sortOrder: z.enum(["asc", "desc"]).default("asc"),
});

export default async function (fastify: FastifyInstance) {
  // Modifiye gruplarını listele
  fastify.get(
    "/",
    {
      onRequest: [fastify.authenticate],
    },
    async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const user = request.user as JWTPayload;
        const query = querySchema.parse(request.query);

        // Where koşulları - ModifierGroup tablosunda companyId yok, global
        const where: any = {
          deletedAt: null,
        };

        // Arama
        if (query.search) {
          where.OR = [
            { name: { contains: query.search, mode: "insensitive" } },
            { description: { contains: query.search, mode: "insensitive" } },
          ];
        }

        // Filtreler
        if (query.active !== undefined) {
          where.active = query.active;
        }

        if (query.required !== undefined) {
          where.required = query.required;
        }

        // Sıralama
        const orderBy: any = {};
        orderBy[query.sortBy] = query.sortOrder;

        // Sayfalama hesaplamaları
        const skip = (query.page - 1) * query.limit;

        // Toplam kayıt sayısı
        const total = await prisma.modifierGroup.count({ where });

        // Modifiye gruplarını getir
        const modifierGroups = await prisma.modifierGroup.findMany({
          where,
          include: {
            modifiers: {
              where: { deletedAt: null },
              select: {
                id: true,
                name: true,
                price: true,
                active: true,
                displayOrder: true,
              },
              orderBy: { displayOrder: "asc" },
            },
            _count: {
              select: {
                modifiers: true,
                products: true,
              },
            },
          },
          orderBy,
          skip,
          take: query.limit,
        });

        // Sayfalama bilgileri
        const totalPages = Math.ceil(total / query.limit);
        const hasNextPage = query.page < totalPages;
        const hasPrevPage = query.page > 1;

        return reply.send({
          success: true,
          data: {
            data: modifierGroups,
            pagination: {
              page: query.page,
              limit: query.limit,
              total,
              totalPages,
              hasNextPage,
              hasPrevPage,
            },
          },
        });
      } catch (error) {
        fastify.log.error(error);
        return reply.status(400).send({
          success: false,
          error: "Bad Request",
          message:
            error instanceof z.ZodError
              ? "Geçersiz sorgu parametreleri"
              : "Modifiye grupları getirilirken bir hata oluştu",
        });
      }
    }
  );

  // Tek modifiye grup detayı
  fastify.get(
    "/:id",
    {
      onRequest: [fastify.authenticate],
    },
    async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const { id } = request.params as { id: string };

        const modifierGroup = await prisma.modifierGroup.findFirst({
          where: {
            id,
            deletedAt: null,
          },
          include: {
            modifiers: {
              where: { deletedAt: null },
              include: {
                inventoryItem: {
                  select: {
                    id: true,
                    name: true,
                    code: true,
                    currentStock: true,
                  },
                },
              },
              orderBy: { displayOrder: "asc" },
            },
            products: {
              include: {
                product: {
                  select: {
                    id: true,
                    name: true,
                    code: true,
                  },
                },
              },
            },
            _count: {
              select: {
                modifiers: true,
                products: true,
              },
            },
          },
        });

        if (!modifierGroup) {
          return reply.status(404).send({
            success: false,
            error: "Not Found",
            message: "Modifiye grup bulunamadı",
          });
        }

        return reply.send({
          success: true,
          data: modifierGroup,
        });
      } catch (error) {
        fastify.log.error(error);
        return reply.status(500).send({
          success: false,
          error: "Internal Server Error",
          message: "Modifiye grup getirilirken bir hata oluştu",
        });
      }
    }
  );

  // Yeni modifiye grup oluştur
  fastify.post(
    "/",
    {
      onRequest: [fastify.authenticate],
    },
    async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const groupData = createModifierGroupSchema.parse(request.body);

        // Modifiye grup oluştur
        const modifierGroup = await prisma.modifierGroup.create({
          data: groupData,
          include: {
            modifiers: {
              where: { deletedAt: null },
              select: {
                id: true,
                name: true,
                price: true,
                active: true,
                displayOrder: true,
              },
              orderBy: { displayOrder: "asc" },
            },
            _count: {
              select: {
                modifiers: true,
                products: true,
              },
            },
          },
        });

        return reply.status(201).send({
          success: true,
          data: modifierGroup,
          message: "Modifiye grup başarıyla oluşturuldu",
        });
      } catch (error) {
        fastify.log.error(error);
        return reply.status(400).send({
          success: false,
          error: "Bad Request",
          message:
            error instanceof z.ZodError
              ? "Geçersiz modifiye grup bilgileri"
              : "Modifiye grup oluşturulurken bir hata oluştu",
        });
      }
    }
  );

  // Modifiye grup güncelle
  fastify.put(
    "/:id",
    {
      onRequest: [fastify.authenticate],
    },
    async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const { id } = request.params as { id: string };
        const groupData = updateModifierGroupSchema.parse(request.body);

        // Mevcut modifiye grup kontrolü
        const existingGroup = await prisma.modifierGroup.findFirst({
          where: {
            id,
            deletedAt: null,
          },
        });

        if (!existingGroup) {
          return reply.status(404).send({
            success: false,
            error: "Not Found",
            message: "Modifiye grup bulunamadı",
          });
        }

        // Modifiye grup güncelle
        const modifierGroup = await prisma.modifierGroup.update({
          where: { id },
          data: groupData,
          include: {
            modifiers: {
              where: { deletedAt: null },
              select: {
                id: true,
                name: true,
                price: true,
                active: true,
                displayOrder: true,
              },
              orderBy: { displayOrder: "asc" },
            },
            _count: {
              select: {
                modifiers: true,
                products: true,
              },
            },
          },
        });

        return reply.send({
          success: true,
          data: modifierGroup,
          message: "Modifiye grup başarıyla güncellendi",
        });
      } catch (error) {
        fastify.log.error(error);
        return reply.status(400).send({
          success: false,
          error: "Bad Request",
          message:
            error instanceof z.ZodError
              ? "Geçersiz modifiye grup bilgileri"
              : "Modifiye grup güncellenirken bir hata oluştu",
        });
      }
    }
  );

  // Modifiye grup sil (soft delete)
  fastify.delete(
    "/:id",
    {
      onRequest: [fastify.authenticate],
    },
    async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const { id } = request.params as { id: string };

        // Mevcut modifiye grup kontrolü
        const existingGroup = await prisma.modifierGroup.findFirst({
          where: {
            id,
            deletedAt: null,
          },
        });

        if (!existingGroup) {
          return reply.status(404).send({
            success: false,
            error: "Not Found",
            message: "Modifiye grup bulunamadı",
          });
        }

        // Soft delete - grup ve tüm modifiyeleri
        await prisma.$transaction([
          // Modifiye grubunu sil
          prisma.modifierGroup.update({
            where: { id },
            data: { deletedAt: new Date() },
          }),
          // Gruba ait tüm modifiyeleri sil
          prisma.modifier.updateMany({
            where: { groupId: id, deletedAt: null },
            data: { deletedAt: new Date() },
          }),
        ]);

        return reply.send({
          success: true,
          message: "Modifiye grup başarıyla silindi",
        });
      } catch (error) {
        fastify.log.error(error);
        return reply.status(500).send({
          success: false,
          error: "Internal Server Error",
          message: "Modifiye grup silinirken bir hata oluştu",
        });
      }
    }
  );

  // Modifiye grup durumunu değiştir (aktif/pasif)
  fastify.put(
    "/:id/toggle-status",
    {
      onRequest: [fastify.authenticate],
    },
    async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const { id } = request.params as { id: string };

        // Mevcut modifiye grup kontrolü
        const existingGroup = await prisma.modifierGroup.findFirst({
          where: {
            id,
            deletedAt: null,
          },
        });

        if (!existingGroup) {
          return reply.status(404).send({
            success: false,
            error: "Not Found",
            message: "Modifiye grup bulunamadı",
          });
        }

        // Durumu değiştir
        const modifierGroup = await prisma.modifierGroup.update({
          where: { id },
          data: { active: !existingGroup.active },
          include: {
            modifiers: {
              where: { deletedAt: null },
              select: {
                id: true,
                name: true,
                price: true,
                active: true,
                displayOrder: true,
              },
              orderBy: { displayOrder: "asc" },
            },
            _count: {
              select: {
                modifiers: true,
                products: true,
              },
            },
          },
        });

        return reply.send({
          success: true,
          data: modifierGroup,
          message: "Modifiye grup durumu başarıyla güncellendi",
        });
      } catch (error) {
        fastify.log.error(error);
        return reply.status(500).send({
          success: false,
          error: "Internal Server Error",
          message: "Modifiye grup durumu güncellenirken bir hata oluştu",
        });
      }
    }
  );
}
